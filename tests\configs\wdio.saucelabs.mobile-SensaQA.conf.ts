import type { Options } from '@wdio/types';
import { join } from 'path';
import { config as sauceSharedConfig } from './wdio.saucelabs.shared.conf.ts';
import { getStepDefinitionFiles } from '../support/helpers/step-definition-finder.ts';

const build = `WebdriverIO - Cucumber - Demo - ${new Date().getTime()}`;

export const config: Options.Testrunner = {
  ...sauceSharedConfig,
  automationProtocol: 'webdriver',
  capabilities: [
    {
      // iPhone 15 Pro Max - Latest real iOS device
      'appium:deviceName': 'iPhone.*',
      'appium:automationName': 'XCUITest',
      'appium:platformVersion': '18.*.*',
      'appium:autoAcceptAlerts': false,
      browserName: 'Safari',
      platformName: 'iOS',
      'sauce:options': {
        build,
        appiumVersion: 'latest',
      },
      webSocketUrl: false,
    },
    // {
    //   // iPhone 15 - Another latest real iOS device
    //   'appium:deviceName': 'iPhone 15',
    //   'appium:automationName': 'XCUITest',
    //   browserName: 'Safari',
    //   platformName: 'iOS',
    //   'sauce:options': {
    //     appiumVersion: 'latest',
    //     build,
    //   },
    // },
    //  {
    //   // Samsung Galaxy S24 Ultra - Latest real Android device

    //   'appium:deviceName': 'Samsung.*',
    //   'appium:automationName': 'UiAutomator2',
    //   'appium:platformVersion': '15',
    //   browserName: 'Chrome',
    //   platformName: 'Android',
    //   'sauce:options': {
    //     appiumVersion: 'stable',
    //     build,

    //   },
    //    // Chrome-specific capabilities at the root level
    //    'goog:chromeOptions': {
    //     args: [
    //         '--start-maximized',
    //         '--disable-notifications',
    //         '--disable-popup-blocking'
    //     ]
    // },
    //   webSocketUrl: false,
    //   'appium:resetKeyboard':true,
    //   'appium:unicodeKeyboard':true
    // },
    // {
    //   // Google Pixel 8 Pro - Latest real Android device
    //   "appium:deviceName": "Google.*",
    //   'appium:platformVersion': '15',
    //   "appium:automationName": "UiAutomator2",
    //   browserName: "Chrome",
    //   platformName: "Android",
    //   "sauce:options": {
    //     appiumVersion: "latest",
    //     build,
    //   },
    //   webSocketUrl: false,
    //   unhandledPromptBehavior:'dismiss'
    // },
  ],

  specs: [join(process.cwd(), './tests/features/sensa/*.feature')],

  // Add these important configurations
  services: [
    [
      'sauce',
      {
        region: 'us-west-1',
      },
    ],
    ['gmail', {
      credentials: join(process.cwd(), 'tests/resources/google-key/gmailCredentials.json'),
      token: join(process.cwd(), 'tests/resources/google-key/token.json'),
      intervalSec: 10,
      timeoutSec: 60,
    }],
  ],

  // Session isolation configuration for scenario-level sessions
  maxInstances: 1, // Force single instance to ensure session isolation
  maxInstancesPerCapability: 1, // One session per capability at a time
  waitforTimeout: 50000,
  connectionRetryTimeout: 150000,
  connectionRetryCount: 3,
  framework: 'cucumber',
  specFileRetries: 0,
  specFileRetriesDelay: 0,
  specFileRetriesDeferred: false,

  // Force session isolation at scenario level
  injectGlobals: true,
  bail: 0, // Don't bail on first failure to allow scenario isolation


  // Cucumber specific configuration
  cucumberOpts: {
    require: getStepDefinitionFiles(),
    backtrace: false,
    requireModule: [],
    dryRun: false,
    failFast: false,
    snippets: true,
    source: true,
    strict: false,
    tags: ' @SensaContactInfo_Update_QA or @SensaSecurityPage_Update_QA or @SensaTobaccoPreferences_Update_QA or @SensaRetaileAccount_Update_QA or @SensaCouponsPage_Validation_QA or @Sensa_flavorpage_Validation_QA or @SensaPreLoginFAQFooterlink_Validation_QA  or @SensaPreLoginContactUsFooterlink_Validation_QA or  @SensaPreLoginSiteRequirementFooterlink_Validation_QA  or  @SensaPreLoginTermsOfUseFooterlink_Validation_QA  or @SensaPreLoginPrivacyPolicyFooterlink_Validation_QA  or @SensaPreLoginTextMessagingFooterlink_Validation_QA or  @SensaPostLoginContactUsFooterlink_Validation_QA or  @SensaPostLoginFAQFooterlink_Validation_QA  or @SensaPostLoginSiteRequirementFooterlink_Validation_QA  or @SensaPostLoginTermsOfUseFooterlink_Validation_QA or @SensaPostLoginPrivacyPolicyFooterlink_Validation_QA or @SensaPostLoginextMessagingFooterlink_Validation_QA or @SensaForgotPasswordFlow_QA  or  @SensaForgotPasswordFlowInvalidUsername_QA  or @SensaForgotPasswordFlowfromForgotUsernamePage_Validation_QA  or  @SensaHomePage_Validation_QA or  @SensaLoginPage_Validation_QA  or @SensaInValidData_Validation_QA or  @SensaRememberMe_Validation_QA or @SensaDevice_Validation_QA or @SensaOffersPage_Validation_QA or @Sensa_Registration_withTobacco_preferences_QA or @Sensa_Register_withAlreadyRegisteredUserofdiffbrand_QA or @Sensa_Register_withAlreadyRegisteredUserofsamebrand_QA or @Sensa_Registration_withoutTobacco_preferences_QA  or @Sensa_Registration_ErrorStep1_QA or @Sensa_Registration_ErrorStep2_QA   or @SensaLSGW_Validation_QA  or  @Sensa_Storelocator_UseMyLocationAndZipcode_Validation_QA or  @Sensa_Storelocator_UseMyLocation_Validation_QA or @Sensa_Storelocator_FilterByProduct_Validation_QA or @SensaValidMobileNumber_Validation_QA  or  @SensaInValidMobileNumber_Validation_QA ',
    timeout: 1600000,
    ignoreUndefinedDefinitions: false,
    retry:2 ,
    retryTagFilter: '', // Only retry scenarios tagged with @flaky
    scenarioLevelReporter: true,
  },
};
